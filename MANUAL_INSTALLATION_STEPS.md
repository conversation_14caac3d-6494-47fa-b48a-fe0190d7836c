# 手动安装步骤 - 完整机器码重置方案

## 🎯 当前状态
✅ VSIX包已创建成功: `./augment-modified.vsix`  
✅ Hook代码已注入到扩展中  
✅ 监控脚本已准备就绪  

## 📋 手动安装步骤

### **第1步: 在VSCode中卸载现有Augment扩展**
1. 打开VSCode
2. 按 `Ctrl+Shift+X` (Windows/Linux) 或 `Cmd+Shift+X` (Mac) 打开扩展面板
3. 搜索 "Augment"
4. 点击已安装的Augment扩展
5. 点击 "卸载" 按钮
6. 重启VSCode

### **第2步: 安装修改后的扩展**
1. 在VSCode中按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
2. 输入 "Extensions: Install from VSIX"
3. 选择该命令
4. 浏览并选择文件: `./augment-modified.vsix`
5. 点击 "安装"
6. 等待安装完成

### **第3步: 重启VSCode**
完全关闭VSCode，然后重新打开，确保扩展完全加载。

## 🔍 开始监控

### **第1步: 打开开发者工具**
1. 在VSCode中，点击菜单 `Help` -> `Toggle Developer Tools`
2. 或者使用快捷键:
   - Windows/Linux: `Ctrl+Shift+I`
   - Mac: `Cmd+Option+I`

### **第2步: 切换到Console标签**
在开发者工具窗口中，点击 "Console" 标签。

### **第3步: 运行监控脚本**
复制以下代码并粘贴到控制台中，然后按回车：

```javascript
// VSCode快速启动监控脚本
console.log('🚀 启动Augment机器码重置监控...');

// 等待扩展加载
setTimeout(() => {
    if (typeof global.getMachineCodeResetLog === 'function') {
        console.log('✅ Hook系统已激活');
        
        // 显示当前状态
        const logs = global.getMachineCodeResetLog();
        console.log(`📊 当前记录: ${logs.length} 个操作`);
        
        // 提供快捷函数
        window.quickReset = async () => {
            console.log('🔄 执行快速重置...');
            if (typeof global.executeCompleteMachineCodeReset === 'function') {
                const result = await global.executeCompleteMachineCodeReset();
                console.log('✅ 重置完成，影响的标识符:', result);
                return result;
            }
        };
        
        window.showLogs = () => {
            const logs = global.getMachineCodeResetLog();
            console.log('📋 所有操作日志:', logs);
            return logs;
        };
        
        window.exportLogs = () => {
            if (typeof global.exportMachineCodeResetLog === 'function') {
                return global.exportMachineCodeResetLog();
            }
        };
        
        console.log('\n💡 可用命令:');
        console.log('  quickReset() - 快速重置所有标识符');
        console.log('  showLogs() - 显示所有操作日志');
        console.log('  exportLogs() - 导出日志到文件');
        
    } else {
        console.log('❌ Hook系统未激活');
        console.log('请确保:');
        console.log('1. 扩展已正确安装');
        console.log('2. VSCode已重新启动');
        console.log('3. 等待扩展完全加载');
    }
}, 2000);
```

## 🎯 验证安装成功

### **预期输出**
如果安装成功，你应该在控制台看到：
```
🚀 启动Augment机器码重置监控...
✅ Hook系统已激活
📊 当前记录: X 个操作
💡 可用命令:
  quickReset() - 快速重置所有标识符
  showLogs() - 显示所有操作日志
  exportLogs() - 导出日志到文件
```

### **如果看到错误**
如果看到 "❌ Hook系统未激活"，请：
1. 确认扩展已正确安装
2. 完全重启VSCode
3. 等待2-3秒让扩展完全加载
4. 重新运行监控脚本

## 🚀 执行重置操作

### **快速重置**
在控制台中运行：
```javascript
quickReset()
```

### **查看操作日志**
```javascript
showLogs()
```

### **导出详细日志**
```javascript
exportLogs()
```

## 📊 监控数据说明

### **操作类型**
- `PROPERTY_DEFINE`: 定义新属性
- `STATE_UPDATE`: 更新状态存储
- `STATE_GET`: 获取状态数据
- `UUID_GENERATION`: 生成UUID
- `NETWORK_REQUEST`: 网络请求
- `JSON_STRINGIFY/PARSE`: JSON操作

### **重要标识符**
监控系统会追踪以下类别的标识符：
- **session**: sessionId, sessionKey, sessionToken
- **client**: clientId, clientKey, clientSecret
- **user**: userId, anonymousId, previousId
- **auth**: accessToken, refreshToken, apiKey
- **fingerprint**: 各种指纹数据
- **storage**: 存储操作
- **network**: 网络相关标识符

## 🔧 故障排除

### **问题1: 扩展安装失败**
- 确保VSCode版本兼容
- 检查VSIX文件是否完整
- 尝试重启VSCode后重新安装

### **问题2: Hook系统未激活**
- 确认扩展已启用
- 检查开发者控制台是否有错误信息
- 等待更长时间让扩展初始化

### **问题3: 重置功能不工作**
- 确认有足够的权限
- 检查VSCode工作区状态
- 查看控制台错误信息

## ⚠️ 安全提醒

1. **备份数据**: 在执行重置前备份重要的VSCode设置
2. **测试环境**: 建议先在测试环境中验证
3. **监控变化**: 注意观察重置后的行为变化
4. **恢复方案**: 保留原始扩展文件以便恢复

## 🎉 预期效果

重置成功后，你可能会观察到：
- Augment扩展的行为发生变化
- 登录界面可能出现不同选项
- 服务器响应可能有所不同
- 新的标识符被重新生成

---

**重要**: 这是一个深度修改扩展的操作，请谨慎使用并做好备份。
