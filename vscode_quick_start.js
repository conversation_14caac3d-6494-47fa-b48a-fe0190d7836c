// VSCode快速启动监控脚本
// 复制此代码到VSCode开发者控制台中运行

console.log('🚀 启动Augment机器码重置监控...');

// 等待扩展加载
setTimeout(() => {
    if (typeof global.getMachineCodeResetLog === 'function') {
        console.log('✅ Hook系统已激活');
        
        // 显示当前状态
        const logs = global.getMachineCodeResetLog();
        console.log(`📊 当前记录: ${logs.length} 个操作`);
        
        // 显示最近的操作
        if (logs.length > 0) {
            console.log('🔍 最近的操作:');
            logs.slice(-5).forEach((log, index) => {
                console.log(`  ${index + 1}. [${log.relativeTime}ms] ${log.type}: ${JSON.stringify(log.details)}`);
            });
        }
        
        // 提供快捷函数
        window.quickReset = async () => {
            console.log('🔄 执行快速重置...');
            if (typeof global.executeCompleteMachineCodeReset === 'function') {
                const result = await global.executeCompleteMachineCodeReset();
                console.log('✅ 重置完成，影响的标识符:', result);
                console.log(`📊 总共重置了 ${result.length} 个标识符`);
                return result;
            } else {
                console.log('❌ 重置函数不可用');
                return null;
            }
        };
        
        window.showLogs = () => {
            const logs = global.getMachineCodeResetLog();
            console.log('📋 所有操作日志:');
            
            // 按类型分组显示
            const groupedLogs = {};
            logs.forEach(log => {
                if (!groupedLogs[log.type]) {
                    groupedLogs[log.type] = [];
                }
                groupedLogs[log.type].push(log);
            });
            
            Object.keys(groupedLogs).forEach(type => {
                console.log(`\n${type}: ${groupedLogs[type].length} 个操作`);
                groupedLogs[type].slice(0, 3).forEach((log, index) => {
                    console.log(`  ${index + 1}. [${log.relativeTime}ms] ${JSON.stringify(log.details)}`);
                });
                if (groupedLogs[type].length > 3) {
                    console.log(`  ... 还有 ${groupedLogs[type].length - 3} 个操作`);
                }
            });
            
            return logs;
        };
        
        window.exportLogs = () => {
            if (typeof global.exportMachineCodeResetLog === 'function') {
                const filename = global.exportMachineCodeResetLog();
                console.log(`📄 日志已导出到: ${filename}`);
                return filename;
            } else {
                console.log('❌ 导出函数不可用');
                return null;
            }
        };
        
        window.monitorRealTime = () => {
            console.log('📡 开始实时监控...');
            let lastLogCount = logs.length;
            
            const interval = setInterval(() => {
                const currentLogs = global.getMachineCodeResetLog();
                if (currentLogs.length > lastLogCount) {
                    const newLogs = currentLogs.slice(lastLogCount);
                    console.log(`📈 新增 ${newLogs.length} 个操作:`);
                    newLogs.forEach((log, index) => {
                        console.log(`  ${index + 1}. [${log.relativeTime}ms] ${log.type}: ${JSON.stringify(log.details)}`);
                    });
                    lastLogCount = currentLogs.length;
                }
            }, 1000);
            
            // 10秒后停止监控
            setTimeout(() => {
                clearInterval(interval);
                console.log('📡 实时监控已停止');
            }, 10000);
            
            return interval;
        };
        
        window.getStats = () => {
            const logs = global.getMachineCodeResetLog();
            const stats = {
                total: logs.length,
                byType: {},
                timeRange: {
                    start: logs.length > 0 ? Math.min(...logs.map(l => l.relativeTime)) : 0,
                    end: logs.length > 0 ? Math.max(...logs.map(l => l.relativeTime)) : 0
                }
            };
            
            logs.forEach(log => {
                stats.byType[log.type] = (stats.byType[log.type] || 0) + 1;
            });
            
            console.log('📊 统计信息:', stats);
            return stats;
        };
        
        console.log('\n💡 可用命令:');
        console.log('  quickReset() - 快速重置所有标识符');
        console.log('  showLogs() - 显示所有操作日志');
        console.log('  exportLogs() - 导出日志到文件');
        console.log('  monitorRealTime() - 开始10秒实时监控');
        console.log('  getStats() - 显示统计信息');
        
        // 自动开始实时监控
        console.log('\n🔄 自动开始5秒实时监控...');
        let autoMonitorCount = 0;
        const autoInterval = setInterval(() => {
            const currentLogs = global.getMachineCodeResetLog();
            if (currentLogs.length > logs.length) {
                const newLogs = currentLogs.slice(logs.length);
                console.log(`📈 检测到 ${newLogs.length} 个新操作`);
                newLogs.forEach((log, index) => {
                    console.log(`  ${index + 1}. [${log.relativeTime}ms] ${log.type}`);
                });
            }
            
            autoMonitorCount++;
            if (autoMonitorCount >= 5) {
                clearInterval(autoInterval);
                console.log('🔄 自动监控结束，使用 monitorRealTime() 继续监控');
            }
        }, 1000);
        
    } else {
        console.log('❌ Hook系统未激活');
        console.log('请确保:');
        console.log('1. 扩展已正确安装 (augment-modified.vsix)');
        console.log('2. VSCode已重新启动');
        console.log('3. 等待扩展完全加载 (约2-3秒)');
        console.log('4. 检查扩展是否已启用');
        
        // 提供重试机制
        window.retryHookCheck = () => {
            console.log('🔄 重新检查Hook系统...');
            setTimeout(() => {
                if (typeof global.getMachineCodeResetLog === 'function') {
                    console.log('✅ Hook系统现在已激活！');
                    location.reload(); // 重新运行脚本
                } else {
                    console.log('❌ Hook系统仍未激活');
                    console.log('💡 尝试: retryHookCheck() 重新检查');
                }
            }, 1000);
        };
        
        console.log('\n💡 可用命令:');
        console.log('  retryHookCheck() - 重新检查Hook系统状态');
    }
}, 2000);
