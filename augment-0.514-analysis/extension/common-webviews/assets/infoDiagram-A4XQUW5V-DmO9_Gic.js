import{_ as o,l as p,I as a,k as e,K as s}from"./AugmentMessage-DE0GCnYX.js";import{p as n}from"./gitGraph-YCYPL57B-cdDr8RuO.js";import"./SpinnerAugment--7_d8Bhb.js";import"./IconButtonAugment-BRmoiBj-.js";import"./CalloutAugment-MqvhzUuY.js";import"./CardAugment-C97U8JyF.js";import"./index-DxPsgTQP.js";import"./async-messaging-4BJNCJIk.js";import"./message-broker-D0HnM_Gz.js";import"./types-CGlLNakm.js";import"./file-paths-DWf4ddOP.js";import"./BaseTextInput-BPG6kKWk.js";import"./folder-opened-Cp5VJX6O.js";import"./index-BNaclw3y.js";import"./diff-operations-f_2vHx7y.js";import"./toggleHighContrast-CKn954qg.js";import"./preload-helper-Dv6uf1Os.js";import"./index-CjD_OSMH.js";import"./keypress-DD1aQVr0.js";import"./await_block-BpK5E--A.js";import"./OpenFileButton-Cm8ysvWC.js";import"./chat-context-yJiWfjLa.js";import"./index-C4gKbsWy.js";import"./remote-agents-client-BbSbxvT9.js";import"./ra-diff-ops-model-Cu1IoT5F.js";import"./TextAreaAugment-28vHVkJ0.js";import"./ButtonAugment-KXhkAj9P.js";import"./CollapseButtonAugment-8NunH2uy.js";import"./partner-mcp-utils-e1yJfVoW.js";import"./MaterialIcon-7jX3JapN.js";import"./CopyButton-BRS6CFKx.js";import"./copy-Clx-mJ-Q.js";import"./ellipsis-DhEzBG8V.js";import"./IconFilePath-BV-yCxAf.js";import"./LanguageIcon-55GMxsQ6.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-rxaIr-vS.js";import"./index-ULzzqGZB.js";import"./pen-to-square-B8s59Zln.js";import"./chevron-down-BJnz7MPg.js";import"./check-BFSTEVRu.js";import"./augment-logo-BSACrS1c.js";import"./_baseUniq-C8aENvKz.js";import"./_basePickBy-M2Gxm2Uy.js";import"./clone-eQ12v26A.js";var d={version:s},or={parser:{parse:o(async r=>{const t=await n("info",r);p.debug(t)},"parse")},db:{getVersion:o(()=>d.version,"getVersion")},renderer:{draw:o((r,t,m)=>{p.debug(`rendering info diagram
`+r);const i=a(t);e(i,100,400,!0),i.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${m}`)},"draw")}};export{or as diagram};
