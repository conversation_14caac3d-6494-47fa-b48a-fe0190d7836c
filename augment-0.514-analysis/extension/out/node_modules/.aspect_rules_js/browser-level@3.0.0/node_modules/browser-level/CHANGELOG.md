# Changelog

## [3.0.0] - 2025-04-20

_If you are upgrading: please see [`UPGRADING.md`](UPGRADING.md)._

### Changed

- **Breaking:** upgrade to `abstract-level` 3 ([#24](https://github.com/Level/browser-level/issues/24)) ([`26adf16`](https://github.com/Level/browser-level/commit/26adf16)) (<PERSON>)

### Added

- Implement `has()` and `hasMany()` ([#25](https://github.com/Level/browser-level/issues/25)) ([`40b1881`](https://github.com/Level/browser-level/commit/40b1881)) (<PERSON>)

## [2.0.0] - 2024-12-01

_If you are upgrading: please see [`UPGRADING.md`](UPGRADING.md)._

### Changed

- **Breaking:** upgrade to `abstract-level` 2 ([#16](https://github.com/Level/browser-level/issues/16)) ([`6296322`](https://github.com/Level/browser-level/commit/6296322)) (<PERSON>)
- Align location check with `classic-level` ([`9304795`](https://github.com/Level/browser-level/commit/9304795)) (Vincent Weevers)

### Removed

- Remove `levelup` compatibility check ([`0b98710`](https://github.com/Level/browser-level/commit/0b98710)) (Vincent Weevers)

## [1.0.1] - 2022-03-06

### Fixed

- Fix TypeScript type declarations ([`d5db420`](https://github.com/Level/browser-level/commit/d5db420)) (Vincent Weevers)

## [1.0.0] - 2022-03-05

_:seedling: Initial release. If you are upgrading from `level-js`, please see [UPGRADING.md](UPGRADING.md)._

[3.0.0]: https://github.com/Level/browser-level/releases/tag/v3.0.0

[2.0.0]: https://github.com/Level/browser-level/releases/tag/v2.0.0

[1.0.1]: https://github.com/Level/browser-level/releases/tag/v1.0.1

[1.0.0]: https://github.com/Level/browser-level/releases/tag/v1.0.0
