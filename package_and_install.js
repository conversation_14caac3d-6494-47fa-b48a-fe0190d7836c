/**
 * 自动打包和安装修改后的VSCode扩展
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class VSCodeExtensionInstaller {
    constructor() {
        this.extensionDir = './augment-0.514-analysis';
        this.outputVsix = './augment-modified.vsix';
        this.extensionId = 'augment.vscode-augment';
    }

    /**
     * 检查必要文件
     */
    checkRequiredFiles() {
        console.log('🔍 检查必要文件...');
        
        const requiredFiles = [
            path.join(this.extensionDir, 'extension/out/extension.js'),
            path.join(this.extensionDir, 'extension/package.json'),
            path.join(this.extensionDir, 'extension.vsixmanifest')
        ];
        
        for (const file of requiredFiles) {
            if (!fs.existsSync(file)) {
                console.error(`❌ 缺少必要文件: ${file}`);
                return false;
            }
        }
        
        console.log('✅ 所有必要文件存在');
        return true;
    }

    /**
     * 验证Hook是否已注入
     */
    verifyHookInjection() {
        console.log('🔍 验证Hook注入状态...');
        
        const extensionFile = path.join(this.extensionDir, 'extension/out/extension.js');
        const content = fs.readFileSync(extensionFile, 'utf8');
        
        if (content.includes('完整机器码重置Hook系统')) {
            console.log('✅ Hook代码已注入');
            return true;
        } else {
            console.log('❌ Hook代码未注入，正在注入...');
            return this.injectHookIfNeeded();
        }
    }

    /**
     * 如果需要，注入Hook代码
     */
    injectHookIfNeeded() {
        try {
            const CompleteMachineCodeReset = require('./complete_machine_code_reset.js');
            const resetSystem = new CompleteMachineCodeReset();
            
            return resetSystem.injectCompleteResetHook();
        } catch (error) {
            console.error('❌ Hook注入失败:', error.message);
            return false;
        }
    }

    /**
     * 创建VSIX包
     */
    createVsixPackage() {
        console.log('📦 创建VSIX包...');
        
        try {
            // 删除旧的VSIX文件
            if (fs.existsSync(this.outputVsix)) {
                fs.unlinkSync(this.outputVsix);
            }
            
            // 使用zip命令创建VSIX包
            const zipCommand = `cd "${this.extensionDir}" && zip -r "../augment-modified.vsix" . -x "*.DS_Store" "*.git*" "node_modules/*"`;
            execSync(zipCommand, { stdio: 'inherit' });
            
            if (fs.existsSync(this.outputVsix)) {
                console.log('✅ VSIX包创建成功:', this.outputVsix);
                return true;
            } else {
                console.error('❌ VSIX包创建失败');
                return false;
            }
        } catch (error) {
            console.error('❌ 打包过程出错:', error.message);
            return false;
        }
    }

    /**
     * 卸载现有扩展
     */
    uninstallExistingExtension() {
        console.log('🗑️  卸载现有Augment扩展...');
        
        try {
            execSync(`code --uninstall-extension ${this.extensionId}`, { stdio: 'inherit' });
            console.log('✅ 现有扩展已卸载');
            return true;
        } catch (error) {
            console.log('⚠️  卸载扩展时出现问题（可能扩展未安装）:', error.message);
            return true; // 继续安装过程
        }
    }

    /**
     * 安装修改后的扩展
     */
    installModifiedExtension() {
        console.log('📥 安装修改后的扩展...');
        
        try {
            const installCommand = `code --install-extension "${this.outputVsix}"`;
            execSync(installCommand, { stdio: 'inherit' });
            console.log('✅ 修改后的扩展安装成功');
            return true;
        } catch (error) {
            console.error('❌ 扩展安装失败:', error.message);
            return false;
        }
    }

    /**
     * 创建快速启动脚本
     */
    createQuickStartScript() {
        const quickStartScript = `
// VSCode快速启动监控脚本
// 复制此代码到VSCode开发者控制台中运行

console.log('🚀 启动Augment机器码重置监控...');

// 等待扩展加载
setTimeout(() => {
    if (typeof global.getMachineCodeResetLog === 'function') {
        console.log('✅ Hook系统已激活');
        
        // 显示当前状态
        const logs = global.getMachineCodeResetLog();
        console.log(\`📊 当前记录: \${logs.length} 个操作\`);
        
        // 提供快捷函数
        window.quickReset = async () => {
            console.log('🔄 执行快速重置...');
            if (typeof global.executeCompleteMachineCodeReset === 'function') {
                const result = await global.executeCompleteMachineCodeReset();
                console.log('✅ 重置完成，影响的标识符:', result);
                return result;
            }
        };
        
        window.showLogs = () => {
            const logs = global.getMachineCodeResetLog();
            console.log('📋 所有操作日志:', logs);
            return logs;
        };
        
        window.exportLogs = () => {
            if (typeof global.exportMachineCodeResetLog === 'function') {
                return global.exportMachineCodeResetLog();
            }
        };
        
        console.log('\\n💡 可用命令:');
        console.log('  quickReset() - 快速重置所有标识符');
        console.log('  showLogs() - 显示所有操作日志');
        console.log('  exportLogs() - 导出日志到文件');
        
    } else {
        console.log('❌ Hook系统未激活');
        console.log('请确保:');
        console.log('1. 扩展已正确安装');
        console.log('2. VSCode已重新启动');
        console.log('3. 等待扩展完全加载');
    }
}, 2000);
`;
        
        fs.writeFileSync('vscode_quick_start.js', quickStartScript);
        console.log('✅ 快速启动脚本已创建: vscode_quick_start.js');
    }

    /**
     * 执行完整的安装流程
     */
    async runCompleteInstallation() {
        console.log('=== VSCode扩展自动安装器 ===\\n');
        
        // 检查必要文件
        if (!this.checkRequiredFiles()) {
            return false;
        }
        
        // 验证Hook注入
        if (!this.verifyHookInjection()) {
            return false;
        }
        
        // 创建VSIX包
        if (!this.createVsixPackage()) {
            return false;
        }
        
        // 卸载现有扩展
        this.uninstallExistingExtension();
        
        // 安装修改后的扩展
        if (!this.installModifiedExtension()) {
            return false;
        }
        
        // 创建快速启动脚本
        this.createQuickStartScript();
        
        console.log('\\n🎉 安装完成！');
        console.log('\\n📋 下一步操作:');
        console.log('1. 重启VSCode (完全关闭后重新打开)');
        console.log('2. 打开开发者工具 (Help -> Toggle Developer Tools)');
        console.log('3. 在Console中粘贴运行 vscode_quick_start.js 的内容');
        console.log('4. 使用 quickReset() 执行重置');
        console.log('\\n⚠️  重要: 请在测试环境中使用，并备份重要数据');
        
        return true;
    }

    /**
     * 恢复原始扩展
     */
    restoreOriginalExtension() {
        console.log('🔄 恢复原始扩展...');
        
        try {
            // 恢复原始文件
            const CompleteMachineCodeReset = require('./complete_machine_code_reset.js');
            const resetSystem = new CompleteMachineCodeReset();
            resetSystem.restoreOriginalFile();
            
            // 重新打包和安装
            this.createVsixPackage();
            this.uninstallExistingExtension();
            this.installModifiedExtension();
            
            console.log('✅ 原始扩展已恢复');
            return true;
        } catch (error) {
            console.error('❌ 恢复失败:', error.message);
            return false;
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const installer = new VSCodeExtensionInstaller();
    
    // 检查命令行参数
    const args = process.argv.slice(2);
    
    if (args.includes('--restore')) {
        installer.restoreOriginalExtension();
    } else {
        installer.runCompleteInstallation();
    }
}

module.exports = VSCodeExtensionInstaller;
