# VSCode安装和监控完整步骤指南

## 🎯 目标
在VSCode中安装修改后的Augment扩展，并实现完整的机器码重置监控。

## 📋 准备工作

### **第1步: 确认文件状态**
确保以下文件已准备好：
```
./augment-0.514-analysis/extension/out/extension.js          # 已注入Hook的扩展文件
./augment-0.514-analysis/extension/out/extension.js.backup  # 原始备份文件
./complete_reset_monitor.js                                 # 监控脚本
```

## 🚀 VSCode安装步骤

### **第1步: 关闭VSCode**
完全关闭所有VSCode窗口，确保扩展不在运行状态。

### **第2步: 卸载现有Augment扩展**
```bash
# 方法1: 通过VSCode界面
# 打开VSCode -> Extensions -> 搜索"Augment" -> 卸载

# 方法2: 通过命令行
code --uninstall-extension augment.vscode-augment
```

### **第3步: 打包修改后的扩展**
```bash
# 进入扩展目录
cd ./augment-0.514-analysis

# 打包成VSIX文件
# 方法1: 如果有vsce工具
vsce package

# 方法2: 手动创建ZIP并重命名
zip -r augment-modified.vsix . -x "*.DS_Store" "*.git*"
mv augment-modified.vsix augment-modified.vsix
```

### **第4步: 安装修改后的扩展**
```bash
# 通过命令行安装
code --install-extension ./augment-0.514-analysis/augment-modified.vsix

# 或者通过VSCode界面
# VSCode -> Extensions -> ... -> Install from VSIX -> 选择文件
```

### **第5步: 重启VSCode**
完全重启VSCode，让扩展重新加载。

## 🔍 监控设置步骤

### **第1步: 打开开发者工具**
```
VSCode菜单 -> Help -> Toggle Developer Tools
或者快捷键: Ctrl+Shift+I (Windows/Linux) / Cmd+Option+I (Mac)
```

### **第2步: 切换到Console标签**
在开发者工具中点击"Console"标签。

### **第3步: 运行监控脚本**
将 `complete_reset_monitor.js` 的内容复制粘贴到控制台并按回车执行。

### **第4步: 验证Hook系统激活**
应该看到类似输出：
```
🚀 开始监控完整机器码重置操作...
✅ 完整重置Hook系统已激活
📊 当前已记录 X 个操作
```

## 🛠️ 监控操作指南

### **实时监控**
监控脚本会每2秒检查一次新操作，显示：
- 操作类型（PROPERTY_DEFINE, STATE_UPDATE等）
- 操作时间
- 相关标识符信息

### **手动重置**
在控制台中运行：
```javascript
// 执行完整重置
manualReset()

// 查看重置日志
global.getMachineCodeResetLog()

// 导出详细日志
global.exportMachineCodeResetLog()
```

### **验证重置效果**
重置后观察：
1. 控制台显示重置的标识符数量
2. 扩展行为是否发生变化
3. 服务器响应是否不同

## 🔧 故障排除

### **问题1: 扩展安装失败**
```bash
# 检查VSIX文件完整性
unzip -t augment-modified.vsix

# 检查VSCode版本兼容性
code --version

# 手动清理扩展目录
rm -rf ~/.vscode/extensions/augment.*
```

### **问题2: Hook系统未激活**
```javascript
// 在控制台检查全局对象
console.log(typeof global.getMachineCodeResetLog);

// 如果返回 'undefined'，说明Hook未注入成功
// 需要重新注入Hook代码
```

### **问题3: 权限问题**
```bash
# 修复文件权限
chmod 644 ./augment-0.514-analysis/extension/out/extension.js

# 检查文件是否可写
ls -la ./augment-0.514-analysis/extension/out/extension.js
```

## 📊 监控数据解读

### **操作类型说明**
- `PROPERTY_DEFINE`: 定义新的标识符属性
- `STATE_UPDATE`: 更新VSCode状态存储
- `STATE_GET`: 获取VSCode状态数据
- `UUID_GENERATION`: 生成新的UUID
- `NETWORK_REQUEST`: 发送网络请求
- `JSON_STRINGIFY/PARSE`: JSON数据处理

### **重要标识符类别**
- **session**: sessionId, sessionKey, sessionToken
- **client**: clientId, clientKey, clientSecret
- **user**: userId, anonymousId, previousId
- **auth**: accessToken, refreshToken, apiKey
- **fingerprint**: 各种指纹数据

## 🎯 完整使用流程

### **初始安装**
1. 关闭VSCode
2. 卸载现有Augment扩展
3. 打包并安装修改后的扩展
4. 重启VSCode

### **开始监控**
1. 打开开发者工具
2. 运行监控脚本
3. 验证Hook系统激活

### **执行重置**
1. 在控制台运行 `manualReset()`
2. 观察重置过程和结果
3. 验证扩展行为变化

### **数据分析**
1. 导出详细日志
2. 分析重置前后差异
3. 验证服务器响应变化

## ⚠️ 安全注意事项

### **备份重要数据**
- 扩展设置和配置
- 工作区数据
- 用户偏好设置

### **测试环境**
建议先在测试环境中验证：
- 使用独立的VSCode配置
- 备份重要项目文件
- 准备回滚方案

### **恢复机制**
如需恢复原始状态：
```javascript
// 在Node.js中运行
const resetSystem = new CompleteMachineCodeReset();
resetSystem.restoreOriginalFile();

// 然后重新打包和安装扩展
```

## 📈 预期效果

### **成功指标**
- Hook系统正常激活
- 能够监控到标识符操作
- 重置功能正常工作
- 扩展行为发生预期变化

### **可能的变化**
- 登录界面选项变化
- 服务器返回不同配置
- 用户体验差异
- 新的标识符生成

---

**重要提醒**: 这是一个深度修改扩展的操作，请确保在安全的测试环境中进行，并做好完整的备份。
