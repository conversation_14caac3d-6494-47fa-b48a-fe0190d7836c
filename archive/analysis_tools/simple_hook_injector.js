/**
 * 简化的SessionID Hook注入器
 * 用于在VSCode扩展中追踪sessionId的初始化和赋值
 */

const fs = require('fs');
const path = require('path');

class SimpleHookInjector {
    constructor() {
        this.extensionPath = './augment-0.514-analysis/extension/out/extension.js';
        this.backupPath = './augment-0.514-analysis/extension/out/extension.js.backup';
    }

    /**
     * 生成简化的Hook代码
     */
    generateHookCode() {
        return `
// ===== SessionID Hook 开始 =====
(function() {
    console.log('[SessionID Hook] 系统已激活');
    const logs = [];
    const startTime = Date.now();
    
    // Hook 1: 拦截对象属性设置
    const originalDefineProperty = Object.defineProperty;
    Object.defineProperty = function(obj, prop, descriptor) {
        if (typeof prop === 'string' && prop.toLowerCase().includes('sessionid')) {
            const logEntry = {
                type: 'PROPERTY_DEFINE',
                property: prop,
                value: descriptor.value,
                time: Date.now() - startTime,
                stack: new Error().stack.split('\\n')[2]
            };
            logs.push(logEntry);
            console.log('[SessionID Hook] PROPERTY_DEFINE:', prop, '=', descriptor.value);
        }
        return originalDefineProperty.call(this, obj, prop, descriptor);
    };
    
    // Hook 2: 拦截变量赋值
    const hookVariableAssignment = () => {
        // 监控全局变量
        if (typeof global !== 'undefined') {
            const originalGlobal = global;
            Object.keys(originalGlobal).forEach(key => {
                if (key.toLowerCase().includes('sessionid')) {
                    console.log('[SessionID Hook] 发现全局sessionId变量:', key, '=', originalGlobal[key]);
                }
            });
        }
    };
    
    // Hook 3: 拦截字符串操作
    const originalStringReplace = String.prototype.replace;
    String.prototype.replace = function(searchValue, replaceValue) {
        const result = originalStringReplace.call(this, searchValue, replaceValue);
        if (this.toLowerCase().includes('sessionid') || 
            (typeof result === 'string' && result.toLowerCase().includes('sessionid'))) {
            console.log('[SessionID Hook] STRING_REPLACE:', this, '->', result);
        }
        return result;
    };
    
    // Hook 4: 拦截JSON操作
    const originalJSONParse = JSON.parse;
    JSON.parse = function(text) {
        const result = originalJSONParse.call(this, text);
        if (typeof text === 'string' && text.toLowerCase().includes('sessionid')) {
            console.log('[SessionID Hook] JSON_PARSE sessionId数据:', text);
        }
        return result;
    };
    
    // Hook 5: 拦截随机数生成
    const originalMathRandom = Math.random;
    Math.random = function() {
        const result = originalMathRandom.call(this);
        const stack = new Error().stack;
        if (stack.toLowerCase().includes('sessionid')) {
            console.log('[SessionID Hook] RANDOM for sessionId:', result);
        }
        return result;
    };
    
    // 定期检查变量
    setInterval(hookVariableAssignment, 1000);
    
    // 导出日志函数
    global.getSessionIdLogs = () => logs;
    global.exportSessionIdLogs = () => {
        const filename = \`sessionid_logs_\${Date.now()}.json\`;
        require('fs').writeFileSync(filename, JSON.stringify(logs, null, 2));
        console.log('[SessionID Hook] 日志已导出:', filename);
        return filename;
    };
    
    console.log('[SessionID Hook] 所有Hook已设置完成');
})();
// ===== SessionID Hook 结束 =====

`;
    }

    /**
     * 检查文件是否存在
     */
    checkFiles() {
        if (!fs.existsSync(this.extensionPath)) {
            console.error('❌ 扩展文件不存在:', this.extensionPath);
            return false;
        }
        console.log('✅ 扩展文件存在:', this.extensionPath);
        return true;
    }

    /**
     * 备份原始文件
     */
    backup() {
        try {
            if (!fs.existsSync(this.backupPath)) {
                fs.copyFileSync(this.extensionPath, this.backupPath);
                console.log('✅ 文件已备份:', this.backupPath);
            } else {
                console.log('⚠️  备份文件已存在');
            }
            return true;
        } catch (error) {
            console.error('❌ 备份失败:', error.message);
            return false;
        }
    }

    /**
     * 注入Hook代码
     */
    inject() {
        try {
            const originalContent = fs.readFileSync(this.extensionPath, 'utf8');
            const hookCode = this.generateHookCode();
            const modifiedContent = hookCode + '\n' + originalContent;
            
            fs.writeFileSync(this.extensionPath, modifiedContent);
            console.log('✅ Hook代码已注入');
            return true;
        } catch (error) {
            console.error('❌ 注入失败:', error.message);
            return false;
        }
    }

    /**
     * 恢复原始文件
     */
    restore() {
        try {
            if (fs.existsSync(this.backupPath)) {
                fs.copyFileSync(this.backupPath, this.extensionPath);
                console.log('✅ 文件已恢复');
                return true;
            } else {
                console.error('❌ 备份文件不存在');
                return false;
            }
        } catch (error) {
            console.error('❌ 恢复失败:', error.message);
            return false;
        }
    }

    /**
     * 验证注入
     */
    verify() {
        try {
            const content = fs.readFileSync(this.extensionPath, 'utf8');
            const hasHook = content.includes('SessionID Hook 开始');
            if (hasHook) {
                console.log('✅ Hook注入验证成功');
                return true;
            } else {
                console.log('❌ Hook注入验证失败');
                return false;
            }
        } catch (error) {
            console.error('❌ 验证失败:', error.message);
            return false;
        }
    }

    /**
     * 创建监控脚本
     */
    createMonitor() {
        const monitorScript = `
// SessionID监控脚本 - 在VSCode开发者控制台中运行

console.log('开始监控SessionID操作...');

// 检查Hook是否已激活
if (typeof global.getSessionIdLogs === 'function') {
    console.log('✅ Hook系统已激活');
    
    // 每2秒检查一次日志
    const checkLogs = () => {
        const logs = global.getSessionIdLogs();
        if (logs.length > 0) {
            console.log(\`📊 发现 \${logs.length} 条SessionID操作:\`);
            logs.forEach((log, index) => {
                console.log(\`  \${index + 1}. [\${log.time}ms] \${log.type}: \${log.property} = \${log.value}\`);
            });
        }
    };
    
    // 立即检查一次
    checkLogs();
    
    // 定期检查
    const interval = setInterval(checkLogs, 2000);
    
    // 10秒后导出日志并停止
    setTimeout(() => {
        clearInterval(interval);
        if (typeof global.exportSessionIdLogs === 'function') {
            global.exportSessionIdLogs();
        }
        console.log('监控已停止');
    }, 10000);
    
} else {
    console.log('❌ Hook系统未激活，请确保扩展已重新加载');
}
`;
        
        fs.writeFileSync('sessionid_monitor.js', monitorScript);
        console.log('✅ 监控脚本已创建: sessionid_monitor.js');
    }

    /**
     * 执行完整的注入流程
     */
    run() {
        console.log('=== SessionID Hook 注入器 ===\n');
        
        if (!this.checkFiles()) return false;
        if (!this.backup()) return false;
        if (!this.inject()) return false;
        if (!this.verify()) return false;
        
        this.createMonitor();
        
        console.log('\n📋 下一步操作:');
        console.log('1. 重新加载VSCode扩展 (Ctrl+Shift+P -> "Developer: Reload Window")');
        console.log('2. 打开VSCode开发者控制台 (Help -> Toggle Developer Tools)');
        console.log('3. 在控制台中粘贴并运行 sessionid_monitor.js 的内容');
        console.log('4. 观察SessionID操作日志');
        console.log('5. 完成后运行 injector.restore() 恢复原始文件');
        
        return true;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const injector = new SimpleHookInjector();
    injector.run();
}

module.exports = SimpleHookInjector;
