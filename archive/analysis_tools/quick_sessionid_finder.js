/**
 * 快速SessionID查找器
 * 专门用于快速定位sessionId的初始化位置
 */

const fs = require('fs');

class QuickSessionIdFinder {
    constructor() {
        this.extensionPath = './augment-0.514-analysis/extension/out/extension.js';
    }

    /**
     * 快速查找sessionId相关代码
     */
    findSessionIdCode() {
        console.log('🔍 快速查找sessionId相关代码...');
        
        const content = fs.readFileSync(this.extensionPath, 'utf8');
        const lines = content.split('\n');
        
        const results = [];
        
        // 搜索包含sessionId的行
        lines.forEach((line, index) => {
            if (line.toLowerCase().includes('sessionid')) {
                const lineNumber = index + 1;
                const trimmedLine = line.trim();
                
                // 分析这一行的类型
                let type = 'UNKNOWN';
                if (trimmedLine.includes('=') && !trimmedLine.includes('==') && !trimmedLine.includes('!=')) {
                    type = 'ASSIGNMENT';
                } else if (trimmedLine.includes('function') || trimmedLine.includes('=>')) {
                    type = 'FUNCTION';
                } else if (trimmedLine.includes('.set(') || trimmedLine.includes('.update(')) {
                    type = 'STORAGE_SET';
                } else if (trimmedLine.includes('.get(')) {
                    type = 'STORAGE_GET';
                } else if (trimmedLine.includes('console.log') || trimmedLine.includes('console.')) {
                    type = 'LOG';
                } else if (trimmedLine.includes('new ') || trimmedLine.includes('create')) {
                    type = 'CREATION';
                }
                
                results.push({
                    lineNumber: lineNumber,
                    type: type,
                    content: trimmedLine,
                    isLikelyInit: this.isLikelyInitialization(trimmedLine)
                });
            }
        });
        
        console.log(`✅ 发现 ${results.length} 行包含sessionId的代码`);
        return results;
    }

    /**
     * 判断是否可能是初始化代码
     */
    isLikelyInitialization(line) {
        const initKeywords = [
            'new ', 'create', 'generate', 'init', 'uuid', 'random',
            'Date.now', 'Math.random', 'crypto.', '= ""', '= null',
            'undefined', 'constructor', 'activate'
        ];
        
        return initKeywords.some(keyword => line.toLowerCase().includes(keyword.toLowerCase()));
    }

    /**
     * 查找最可能的初始化位置
     */
    findMostLikelyInitialization() {
        const allResults = this.findSessionIdCode();
        
        // 过滤出最可能的初始化代码
        const initCandidates = allResults.filter(result => 
            result.isLikelyInit || 
            result.type === 'ASSIGNMENT' || 
            result.type === 'CREATION'
        );
        
        console.log('\n🎯 最可能的sessionId初始化位置:');
        initCandidates.forEach((candidate, index) => {
            console.log(`${index + 1}. [行${candidate.lineNumber}] ${candidate.type}`);
            console.log(`   ${candidate.content}`);
            console.log('');
        });
        
        return initCandidates;
    }

    /**
     * 搜索特定模式
     */
    searchSpecificPatterns() {
        console.log('🔍 搜索特定的sessionId模式...');
        
        const content = fs.readFileSync(this.extensionPath, 'utf8');
        
        const patterns = [
            {
                name: 'UUID生成',
                regex: /sessionid[^=]*=\s*[^;]*uuid[^;]*/gi
            },
            {
                name: '随机数生成',
                regex: /sessionid[^=]*=\s*[^;]*random[^;]*/gi
            },
            {
                name: '时间戳生成',
                regex: /sessionid[^=]*=\s*[^;]*date\.now[^;]*/gi
            },
            {
                name: '字符串拼接',
                regex: /sessionid[^=]*=\s*[^;]*\+[^;]*/gi
            },
            {
                name: '函数调用生成',
                regex: /sessionid[^=]*=\s*[^;(]*\([^)]*\)[^;]*/gi
            }
        ];
        
        const patternResults = [];
        
        patterns.forEach(pattern => {
            let match;
            while ((match = pattern.regex.exec(content)) !== null) {
                const lineNumber = content.substring(0, match.index).split('\n').length;
                patternResults.push({
                    pattern: pattern.name,
                    match: match[0],
                    lineNumber: lineNumber,
                    position: match.index
                });
            }
        });
        
        console.log(`✅ 发现 ${patternResults.length} 个特定模式匹配`);
        
        patternResults.forEach((result, index) => {
            console.log(`${index + 1}. [行${result.lineNumber}] ${result.pattern}`);
            console.log(`   ${result.match}`);
            console.log('');
        });
        
        return patternResults;
    }

    /**
     * 查找函数定义
     */
    findSessionIdFunctions() {
        console.log('🔍 查找sessionId相关函数...');
        
        const content = fs.readFileSync(this.extensionPath, 'utf8');
        
        // 查找包含sessionId的函数定义
        const functionPattern = /function\s+[^(]*sessionid[^(]*\([^)]*\)|[^(]*sessionid[^(]*:\s*function\s*\([^)]*\)|[^(]*sessionid[^(]*\s*=\s*\([^)]*\)\s*=>/gi;
        
        const functions = [];
        let match;
        
        while ((match = functionPattern.exec(content)) !== null) {
            const lineNumber = content.substring(0, match.index).split('\n').length;
            functions.push({
                definition: match[0],
                lineNumber: lineNumber,
                position: match.index
            });
        }
        
        console.log(`✅ 发现 ${functions.length} 个sessionId相关函数`);
        
        functions.forEach((func, index) => {
            console.log(`${index + 1}. [行${func.lineNumber}] ${func.definition}`);
        });
        
        return functions;
    }

    /**
     * 生成简化报告
     */
    generateQuickReport() {
        console.log('=== 快速SessionID分析报告 ===\n');
        
        const allCode = this.findSessionIdCode();
        const initCandidates = this.findMostLikelyInitialization();
        const patterns = this.searchSpecificPatterns();
        const functions = this.findSessionIdFunctions();
        
        const report = {
            summary: {
                totalLines: allCode.length,
                initCandidates: initCandidates.length,
                patterns: patterns.length,
                functions: functions.length
            },
            topInitCandidates: initCandidates.slice(0, 5),
            topPatterns: patterns.slice(0, 3),
            allFunctions: functions
        };
        
        console.log('\n📊 分析摘要:');
        console.log(`  总共发现: ${report.summary.totalLines} 行sessionId代码`);
        console.log(`  初始化候选: ${report.summary.initCandidates} 个`);
        console.log(`  特定模式: ${report.summary.patterns} 个`);
        console.log(`  相关函数: ${report.summary.functions} 个`);
        
        // 导出报告
        const filename = `quick_sessionid_report_${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(report, null, 2));
        console.log(`\n📄 报告已导出: ${filename}`);
        
        return report;
    }

    /**
     * 运行快速分析
     */
    runQuickAnalysis() {
        console.log('🚀 开始快速SessionID分析...\n');
        return this.generateQuickReport();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const finder = new QuickSessionIdFinder();
    finder.runQuickAnalysis();
}

module.exports = QuickSessionIdFinder;
