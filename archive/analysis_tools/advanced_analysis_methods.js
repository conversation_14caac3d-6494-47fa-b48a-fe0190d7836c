/**
 * 高级分析方法：替代IDA的JavaScript代码分析方案
 * 专门用于分析VSCode扩展中的sessionId初始化
 */

const fs = require('fs');
const path = require('path');

class AdvancedJSAnalyzer {
    constructor() {
        this.extensionPath = './augment-0.514-analysis/extension/out/extension.js';
        this.analysisResults = {
            sessionIdPatterns: [],
            functionEntries: [],
            initializationPoints: [],
            callChains: []
        };
    }

    /**
     * 方法1: 静态模式分析 - 寻找sessionId初始化模式
     */
    analyzeSessionIdPatterns() {
        console.log('🔍 开始静态模式分析...');
        
        const content = fs.readFileSync(this.extensionPath, 'utf8');
        
        // 模式1: 直接赋值模式
        const directAssignmentPattern = /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*[=:]\s*['""][^'"]*sessionid[^'"]*['""]|([a-zA-Z_$][a-zA-Z0-9_$]*)\s*[=:]\s*[^;,}]+sessionid[^;,}]*/gi;
        
        // 模式2: 函数调用模式
        const functionCallPattern = /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(\s*[^)]*sessionid[^)]*/gi;
        
        // 模式3: 对象属性模式
        const objectPropertyPattern = /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\.\s*sessionid\s*[=:]/gi;
        
        // 模式4: 数组/Map操作模式
        const mapSetPattern = /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\.\s*set\s*\(\s*['""]?[^'"]*sessionid[^'"]*['""]?\s*,/gi;
        
        const patterns = [
            { name: 'DirectAssignment', regex: directAssignmentPattern },
            { name: 'FunctionCall', regex: functionCallPattern },
            { name: 'ObjectProperty', regex: objectPropertyPattern },
            { name: 'MapSet', regex: mapSetPattern }
        ];
        
        patterns.forEach(pattern => {
            let match;
            while ((match = pattern.regex.exec(content)) !== null) {
                const context = this.getContext(content, match.index, 100);
                this.analysisResults.sessionIdPatterns.push({
                    type: pattern.name,
                    match: match[0],
                    variable: match[1],
                    position: match.index,
                    context: context,
                    lineNumber: this.getLineNumber(content, match.index)
                });
            }
        });
        
        console.log(`✅ 发现 ${this.analysisResults.sessionIdPatterns.length} 个sessionId模式`);
        return this.analysisResults.sessionIdPatterns;
    }

    /**
     * 方法2: 函数入口点分析
     */
    analyzeFunctionEntries() {
        console.log('🔍 开始函数入口点分析...');
        
        const content = fs.readFileSync(this.extensionPath, 'utf8');
        
        // 查找可能的入口函数
        const entryPatterns = [
            // VSCode扩展激活函数
            /function\s+activate\s*\([^)]*\)\s*{/gi,
            /activate\s*:\s*function\s*\([^)]*\)\s*{/gi,
            /activate\s*:\s*\([^)]*\)\s*=>\s*{/gi,
            
            // 命令注册函数
            /registerCommand\s*\([^)]*\)\s*{/gi,
            /commands\.register[^(]*\([^)]*\)\s*{/gi,
            
            // 重置相关函数
            /function\s+[^(]*reset[^(]*\([^)]*\)\s*{/gi,
            /[^(]*reset[^(]*:\s*function\s*\([^)]*\)\s*{/gi,
            
            // 初始化函数
            /function\s+[^(]*init[^(]*\([^)]*\)\s*{/gi,
            /[^(]*init[^(]*:\s*function\s*\([^)]*\)\s*{/gi
        ];
        
        entryPatterns.forEach((pattern, index) => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                const context = this.getContext(content, match.index, 200);
                const functionBody = this.extractFunctionBody(content, match.index);
                
                this.analysisResults.functionEntries.push({
                    type: `EntryPattern${index + 1}`,
                    match: match[0],
                    position: match.index,
                    lineNumber: this.getLineNumber(content, match.index),
                    context: context,
                    functionBody: functionBody.substring(0, 500), // 限制长度
                    hasSessionId: functionBody.toLowerCase().includes('sessionid')
                });
            }
        });
        
        console.log(`✅ 发现 ${this.analysisResults.functionEntries.length} 个函数入口点`);
        return this.analysisResults.functionEntries;
    }

    /**
     * 方法3: 初始化时序分析
     */
    analyzeInitializationSequence() {
        console.log('🔍 开始初始化时序分析...');
        
        const content = fs.readFileSync(this.extensionPath, 'utf8');
        
        // 查找初始化相关的代码块
        const initPatterns = [
            // 立即执行函数
            /\(\s*function\s*\([^)]*\)\s*{[^}]*sessionid[^}]*}/gi,
            /\(\s*\([^)]*\)\s*=>\s*{[^}]*sessionid[^}]*}/gi,
            
            // 模块初始化
            /module\.exports\s*=\s*{[^}]*sessionid[^}]*}/gi,
            /exports\.[^=]*=\s*[^;]*sessionid[^;]*/gi,
            
            // 类构造函数
            /constructor\s*\([^)]*\)\s*{[^}]*sessionid[^}]*}/gi,
            /function\s+[A-Z][^(]*\([^)]*\)\s*{[^}]*sessionid[^}]*}/gi
        ];
        
        initPatterns.forEach((pattern, index) => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                const context = this.getContext(content, match.index, 300);
                
                this.analysisResults.initializationPoints.push({
                    type: `InitPattern${index + 1}`,
                    match: match[0],
                    position: match.index,
                    lineNumber: this.getLineNumber(content, match.index),
                    context: context,
                    priority: this.calculateInitPriority(match[0])
                });
            }
        });
        
        // 按优先级排序
        this.analysisResults.initializationPoints.sort((a, b) => b.priority - a.priority);
        
        console.log(`✅ 发现 ${this.analysisResults.initializationPoints.length} 个初始化点`);
        return this.analysisResults.initializationPoints;
    }

    /**
     * 方法4: 调用链分析
     */
    analyzeCallChains() {
        console.log('🔍 开始调用链分析...');
        
        const content = fs.readFileSync(this.extensionPath, 'utf8');
        
        // 查找sessionId相关的函数调用链
        const sessionIdMatches = [];
        const sessionIdPattern = /sessionid/gi;
        let match;
        
        while ((match = sessionIdPattern.exec(content)) !== null) {
            const context = this.getContext(content, match.index, 150);
            const functionName = this.extractFunctionName(content, match.index);
            const callers = this.findCallers(content, functionName);
            
            sessionIdMatches.push({
                position: match.index,
                lineNumber: this.getLineNumber(content, match.index),
                context: context,
                functionName: functionName,
                callers: callers
            });
        }
        
        // 构建调用链
        sessionIdMatches.forEach(match => {
            if (match.functionName && match.callers.length > 0) {
                this.analysisResults.callChains.push({
                    target: match.functionName,
                    callers: match.callers,
                    position: match.position,
                    lineNumber: match.lineNumber,
                    depth: match.callers.length
                });
            }
        });
        
        console.log(`✅ 发现 ${this.analysisResults.callChains.length} 个调用链`);
        return this.analysisResults.callChains;
    }

    /**
     * 辅助方法: 获取上下文
     */
    getContext(content, position, length) {
        const start = Math.max(0, position - length);
        const end = Math.min(content.length, position + length);
        return content.substring(start, end);
    }

    /**
     * 辅助方法: 获取行号
     */
    getLineNumber(content, position) {
        return content.substring(0, position).split('\n').length;
    }

    /**
     * 辅助方法: 提取函数体
     */
    extractFunctionBody(content, startPosition) {
        let braceCount = 0;
        let inFunction = false;
        let functionBody = '';
        
        for (let i = startPosition; i < content.length; i++) {
            const char = content[i];
            
            if (char === '{') {
                braceCount++;
                inFunction = true;
            } else if (char === '}') {
                braceCount--;
            }
            
            if (inFunction) {
                functionBody += char;
            }
            
            if (inFunction && braceCount === 0) {
                break;
            }
        }
        
        return functionBody;
    }

    /**
     * 辅助方法: 提取函数名
     */
    extractFunctionName(content, position) {
        // 向前查找函数定义
        const beforeContext = content.substring(Math.max(0, position - 200), position);
        const functionMatch = beforeContext.match(/function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)|([a-zA-Z_$][a-zA-Z0-9_$]*)\s*[:=]\s*function/);
        
        if (functionMatch) {
            return functionMatch[1] || functionMatch[2];
        }
        
        return null;
    }

    /**
     * 辅助方法: 查找调用者
     */
    findCallers(content, functionName) {
        if (!functionName) return [];
        
        const callers = [];
        const callPattern = new RegExp(`([a-zA-Z_$][a-zA-Z0-9_$]*)\\.${functionName}\\s*\\(|${functionName}\\s*\\(`, 'gi');
        let match;
        
        while ((match = callPattern.exec(content)) !== null) {
            const context = this.getContext(content, match.index, 100);
            callers.push({
                caller: match[1] || 'direct',
                position: match.index,
                context: context
            });
        }
        
        return callers.slice(0, 5); // 限制数量
    }

    /**
     * 辅助方法: 计算初始化优先级
     */
    calculateInitPriority(code) {
        let priority = 0;
        
        // 立即执行函数优先级高
        if (code.includes('(function') || code.includes('(() =>')) priority += 10;
        
        // 构造函数优先级高
        if (code.includes('constructor') || code.includes('function [A-Z]')) priority += 8;
        
        // 模块导出优先级中等
        if (code.includes('module.exports') || code.includes('exports.')) priority += 5;
        
        // 包含sessionId的优先级更高
        if (code.toLowerCase().includes('sessionid')) priority += 15;
        
        return priority;
    }

    /**
     * 生成分析报告
     */
    generateReport() {
        console.log('\n=== 高级JavaScript分析报告 ===');
        
        const report = {
            summary: {
                sessionIdPatterns: this.analysisResults.sessionIdPatterns.length,
                functionEntries: this.analysisResults.functionEntries.length,
                initializationPoints: this.analysisResults.initializationPoints.length,
                callChains: this.analysisResults.callChains.length
            },
            topFindings: {
                mostLikelyInitPoints: this.analysisResults.initializationPoints.slice(0, 3),
                sessionIdAssignments: this.analysisResults.sessionIdPatterns.filter(p => p.type === 'DirectAssignment'),
                entryFunctions: this.analysisResults.functionEntries.filter(f => f.hasSessionId)
            },
            fullResults: this.analysisResults
        };
        
        console.log('📊 分析摘要:');
        console.log(`  SessionID模式: ${report.summary.sessionIdPatterns}`);
        console.log(`  函数入口点: ${report.summary.functionEntries}`);
        console.log(`  初始化点: ${report.summary.initializationPoints}`);
        console.log(`  调用链: ${report.summary.callChains}`);
        
        console.log('\n🎯 重点发现:');
        report.topFindings.mostLikelyInitPoints.forEach((point, index) => {
            console.log(`  ${index + 1}. [行${point.lineNumber}] ${point.type} (优先级: ${point.priority})`);
        });
        
        return report;
    }

    /**
     * 导出分析结果
     */
    exportResults() {
        const report = this.generateReport();
        const filename = `advanced_analysis_${Date.now()}.json`;
        
        fs.writeFileSync(filename, JSON.stringify(report, null, 2));
        console.log(`\n📄 详细报告已导出: ${filename}`);
        
        return filename;
    }

    /**
     * 执行完整分析
     */
    runFullAnalysis() {
        console.log('🚀 开始高级JavaScript分析...\n');
        
        this.analyzeSessionIdPatterns();
        this.analyzeFunctionEntries();
        this.analyzeInitializationSequence();
        this.analyzeCallChains();
        
        return this.exportResults();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const analyzer = new AdvancedJSAnalyzer();
    analyzer.runFullAnalysis();
}

module.exports = AdvancedJSAnalyzer;
