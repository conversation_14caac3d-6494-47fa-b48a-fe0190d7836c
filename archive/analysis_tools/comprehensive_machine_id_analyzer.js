/**
 * 综合机器码标识符分析器
 * 发现所有可能需要重置的机器码相关标识符
 */

const fs = require('fs');

class ComprehensiveMachineIdAnalyzer {
    constructor() {
        this.extensionPath = './augment-0.514-analysis/extension/out/extension.js';
        this.identifierCategories = {
            session: [],      // 会话相关
            client: [],       // 客户端相关
            device: [],       // 设备相关
            user: [],         // 用户相关
            machine: [],      // 机器相关
            installation: [], // 安装相关
            fingerprint: [],  // 指纹相关
            auth: [],         // 认证相关
            storage: [],      // 存储相关
            network: []       // 网络相关
        };
    }

    /**
     * 搜索所有可能的机器码标识符
     */
    findAllMachineIdentifiers() {
        console.log('🔍 开始搜索所有机器码标识符...');
        
        const content = fs.readFileSync(this.extensionPath, 'utf8');
        const lines = content.split('\n');
        
        // 定义标识符模式
        const identifierPatterns = {
            session: [
                /sessionid/gi, /session_id/gi, /session-id/gi,
                /sessionkey/gi, /session_key/gi, /session-key/gi,
                /sessiontoken/gi, /session_token/gi, /session-token/gi
            ],
            client: [
                /clientid/gi, /client_id/gi, /client-id/gi,
                /clientkey/gi, /client_key/gi, /client-key/gi,
                /clientsecret/gi, /client_secret/gi, /client-secret/gi,
                /clienttoken/gi, /client_token/gi, /client-token/gi
            ],
            device: [
                /deviceid/gi, /device_id/gi, /device-id/gi,
                /devicekey/gi, /device_key/gi, /device-key/gi,
                /devicefingerprint/gi, /device_fingerprint/gi,
                /hardwareid/gi, /hardware_id/gi, /hardware-id/gi
            ],
            user: [
                /userid/gi, /user_id/gi, /user-id/gi,
                /userkey/gi, /user_key/gi, /user-key/gi,
                /anonymousid/gi, /anonymous_id/gi, /anonymous-id/gi,
                /previousid/gi, /previous_id/gi, /previous-id/gi
            ],
            machine: [
                /machineid/gi, /machine_id/gi, /machine-id/gi,
                /machinekey/gi, /machine_key/gi, /machine-key/gi,
                /machinefingerprint/gi, /machine_fingerprint/gi,
                /computerid/gi, /computer_id/gi, /computer-id/gi
            ],
            installation: [
                /installationid/gi, /installation_id/gi, /installation-id/gi,
                /installid/gi, /install_id/gi, /install-id/gi,
                /instanceid/gi, /instance_id/gi, /instance-id/gi,
                /appid/gi, /app_id/gi, /app-id/gi
            ],
            fingerprint: [
                /fingerprint/gi, /finger_print/gi, /finger-print/gi,
                /signature/gi, /hash/gi, /checksum/gi,
                /uniqueid/gi, /unique_id/gi, /unique-id/gi,
                /identifier/gi, /uuid/gi, /guid/gi
            ],
            auth: [
                /accesstoken/gi, /access_token/gi, /access-token/gi,
                /refreshtoken/gi, /refresh_token/gi, /refresh-token/gi,
                /authtoken/gi, /auth_token/gi, /auth-token/gi,
                /bearertoken/gi, /bearer_token/gi, /bearer-token/gi,
                /apikey/gi, /api_key/gi, /api-key/gi
            ],
            storage: [
                /globalstate/gi, /global_state/gi, /global-state/gi,
                /workspacestate/gi, /workspace_state/gi, /workspace-state/gi,
                /localstorage/gi, /local_storage/gi, /local-storage/gi,
                /cache/gi, /storage/gi, /persist/gi
            ],
            network: [
                /ipaddress/gi, /ip_address/gi, /ip-address/gi,
                /macaddress/gi, /mac_address/gi, /mac-address/gi,
                /hostname/gi, /host_name/gi, /host-name/gi,
                /useragent/gi, /user_agent/gi, /user-agent/gi
            ]
        };

        // 搜索每个类别的标识符
        Object.keys(identifierPatterns).forEach(category => {
            console.log(`\n🔍 搜索 ${category} 类别标识符...`);
            
            identifierPatterns[category].forEach(pattern => {
                lines.forEach((line, index) => {
                    const matches = line.match(pattern);
                    if (matches) {
                        matches.forEach(match => {
                            const context = this.extractContext(line, match);
                            const type = this.analyzeIdentifierType(line);
                            
                            this.identifierCategories[category].push({
                                match: match,
                                lineNumber: index + 1,
                                context: context,
                                type: type,
                                line: line.trim(),
                                isAssignment: line.includes('=') && !line.includes('=='),
                                isStorage: line.includes('.update(') || line.includes('.set('),
                                isGeneration: line.includes('new ') || line.includes('create') || line.includes('generate')
                            });
                        });
                    }
                });
            });
            
            console.log(`✅ ${category}: 发现 ${this.identifierCategories[category].length} 个标识符`);
        });
    }

    /**
     * 提取上下文信息
     */
    extractContext(line, match) {
        const index = line.indexOf(match);
        const start = Math.max(0, index - 30);
        const end = Math.min(line.length, index + match.length + 30);
        return line.substring(start, end);
    }

    /**
     * 分析标识符类型
     */
    analyzeIdentifierType(line) {
        if (line.includes('=') && !line.includes('==') && !line.includes('!=')) {
            return 'ASSIGNMENT';
        } else if (line.includes('.update(') || line.includes('.set(')) {
            return 'STORAGE_SET';
        } else if (line.includes('.get(')) {
            return 'STORAGE_GET';
        } else if (line.includes('function') || line.includes('=>')) {
            return 'FUNCTION';
        } else if (line.includes('new ') || line.includes('create')) {
            return 'CREATION';
        } else if (line.includes('console.')) {
            return 'LOG';
        } else {
            return 'REFERENCE';
        }
    }

    /**
     * 查找重置相关的函数
     */
    findResetFunctions() {
        console.log('\n🔍 搜索重置相关函数...');
        
        const content = fs.readFileSync(this.extensionPath, 'utf8');
        const resetFunctions = [];
        
        // 重置相关的模式
        const resetPatterns = [
            /function\s+[^(]*reset[^(]*\([^)]*\)/gi,
            /[^(]*reset[^(]*:\s*function\s*\([^)]*\)/gi,
            /[^(]*reset[^(]*\s*=\s*\([^)]*\)\s*=>/gi,
            /function\s+[^(]*clear[^(]*\([^)]*\)/gi,
            /[^(]*clear[^(]*:\s*function\s*\([^)]*\)/gi,
            /function\s+[^(]*refresh[^(]*\([^)]*\)/gi,
            /registerCommand\s*\([^)]*reset[^)]*\)/gi
        ];
        
        resetPatterns.forEach((pattern, index) => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                const lineNumber = content.substring(0, match.index).split('\n').length;
                const context = this.getContext(content, match.index, 200);
                
                resetFunctions.push({
                    type: `ResetPattern${index + 1}`,
                    match: match[0],
                    lineNumber: lineNumber,
                    context: context
                });
            }
        });
        
        console.log(`✅ 发现 ${resetFunctions.length} 个重置相关函数`);
        return resetFunctions;
    }

    /**
     * 获取上下文
     */
    getContext(content, position, length) {
        const start = Math.max(0, position - length);
        const end = Math.min(content.length, position + length);
        return content.substring(start, end);
    }

    /**
     * 分析存储键值
     */
    analyzeStorageKeys() {
        console.log('\n🔍 分析存储键值...');
        
        const content = fs.readFileSync(this.extensionPath, 'utf8');
        const storageKeys = [];
        
        // 搜索存储操作
        const storagePatterns = [
            /\.update\s*\(\s*['""]([^'""]+)['""][^)]*\)/gi,
            /\.set\s*\(\s*['""]([^'""]+)['""][^)]*\)/gi,
            /\.get\s*\(\s*['""]([^'""]+)['""][^)]*\)/gi,
            /globalState\.[^(]*\(\s*['""]([^'""]+)['""][^)]*\)/gi,
            /workspaceState\.[^(]*\(\s*['""]([^'""]+)['""][^)]*\)/gi
        ];
        
        storagePatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                const key = match[1];
                const lineNumber = content.substring(0, match.index).split('\n').length;
                
                storageKeys.push({
                    key: key,
                    operation: match[0],
                    lineNumber: lineNumber,
                    isIdRelated: this.isIdentifierRelated(key)
                });
            }
        });
        
        console.log(`✅ 发现 ${storageKeys.length} 个存储键值`);
        return storageKeys;
    }

    /**
     * 判断是否与标识符相关
     */
    isIdentifierRelated(key) {
        const idKeywords = [
            'id', 'key', 'token', 'session', 'client', 'user', 'device',
            'machine', 'installation', 'fingerprint', 'auth', 'uuid',
            'guid', 'hash', 'signature', 'anonymous', 'previous'
        ];
        
        return idKeywords.some(keyword => 
            key.toLowerCase().includes(keyword.toLowerCase())
        );
    }

    /**
     * 生成综合重置方案
     */
    generateResetPlan() {
        console.log('\n🎯 生成综合重置方案...');
        
        const resetPlan = {
            coreIdentifiers: [],
            storageKeys: [],
            resetFunctions: [],
            priority: {
                high: [],
                medium: [],
                low: []
            }
        };
        
        // 收集所有标识符
        Object.keys(this.identifierCategories).forEach(category => {
            this.identifierCategories[category].forEach(identifier => {
                if (identifier.isAssignment || identifier.isStorage || identifier.isGeneration) {
                    resetPlan.coreIdentifiers.push({
                        category: category,
                        identifier: identifier.match,
                        lineNumber: identifier.lineNumber,
                        type: identifier.type,
                        context: identifier.context
                    });
                    
                    // 分配优先级
                    if (category === 'session' || category === 'client' || category === 'auth') {
                        resetPlan.priority.high.push(identifier.match);
                    } else if (category === 'device' || category === 'machine' || category === 'user') {
                        resetPlan.priority.medium.push(identifier.match);
                    } else {
                        resetPlan.priority.low.push(identifier.match);
                    }
                }
            });
        });
        
        return resetPlan;
    }

    /**
     * 生成完整报告
     */
    generateComprehensiveReport() {
        console.log('\n=== 综合机器码标识符分析报告 ===');
        
        const resetFunctions = this.findResetFunctions();
        const storageKeys = this.analyzeStorageKeys();
        const resetPlan = this.generateResetPlan();
        
        const report = {
            summary: {
                totalIdentifiers: Object.values(this.identifierCategories)
                    .reduce((sum, cat) => sum + cat.length, 0),
                categoryCounts: Object.keys(this.identifierCategories)
                    .reduce((obj, key) => {
                        obj[key] = this.identifierCategories[key].length;
                        return obj;
                    }, {}),
                resetFunctions: resetFunctions.length,
                storageKeys: storageKeys.length,
                idRelatedKeys: storageKeys.filter(k => k.isIdRelated).length
            },
            identifierCategories: this.identifierCategories,
            resetFunctions: resetFunctions,
            storageKeys: storageKeys.filter(k => k.isIdRelated),
            resetPlan: resetPlan,
            recommendations: this.generateRecommendations(resetPlan)
        };
        
        // 打印摘要
        console.log('\n📊 分析摘要:');
        console.log(`  总标识符数: ${report.summary.totalIdentifiers}`);
        Object.keys(report.summary.categoryCounts).forEach(category => {
            console.log(`  ${category}: ${report.summary.categoryCounts[category]}`);
        });
        console.log(`  重置函数: ${report.summary.resetFunctions}`);
        console.log(`  存储键值: ${report.summary.storageKeys}`);
        console.log(`  ID相关键: ${report.summary.idRelatedKeys}`);
        
        console.log('\n🎯 重置优先级:');
        console.log(`  高优先级: ${resetPlan.priority.high.length} 个`);
        console.log(`  中优先级: ${resetPlan.priority.medium.length} 个`);
        console.log(`  低优先级: ${resetPlan.priority.low.length} 个`);
        
        return report;
    }

    /**
     * 生成建议
     */
    generateRecommendations(resetPlan) {
        return {
            immediate: [
                '重置所有session相关标识符',
                '清除client认证信息',
                '重新生成设备指纹'
            ],
            followUp: [
                '清理本地存储缓存',
                '重置网络相关标识符',
                '更新用户偏好设置'
            ],
            monitoring: [
                '监控重置后的行为变化',
                '验证新标识符的生成',
                '确认服务器响应差异'
            ]
        };
    }

    /**
     * 导出结果
     */
    exportResults() {
        const report = this.generateComprehensiveReport();
        const filename = `comprehensive_machine_id_analysis_${Date.now()}.json`;
        
        fs.writeFileSync(filename, JSON.stringify(report, null, 2));
        console.log(`\n📄 详细报告已导出: ${filename}`);
        
        return filename;
    }

    /**
     * 运行完整分析
     */
    runFullAnalysis() {
        console.log('🚀 开始综合机器码标识符分析...\n');
        
        this.findAllMachineIdentifiers();
        return this.exportResults();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const analyzer = new ComprehensiveMachineIdAnalyzer();
    analyzer.runFullAnalysis();
}

module.exports = ComprehensiveMachineIdAnalyzer;
