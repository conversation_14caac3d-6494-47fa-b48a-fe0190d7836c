
// SessionID监控脚本 - 在VSCode开发者控制台中运行

console.log('开始监控SessionID操作...');

// 检查Hook是否已激活
if (typeof global.getSessionIdLogs === 'function') {
    console.log('✅ Hook系统已激活');
    
    // 每2秒检查一次日志
    const checkLogs = () => {
        const logs = global.getSessionIdLogs();
        if (logs.length > 0) {
            console.log(`📊 发现 ${logs.length} 条SessionID操作:`);
            logs.forEach((log, index) => {
                console.log(`  ${index + 1}. [${log.time}ms] ${log.type}: ${log.property} = ${log.value}`);
            });
        }
    };
    
    // 立即检查一次
    checkLogs();
    
    // 定期检查
    const interval = setInterval(checkLogs, 2000);
    
    // 10秒后导出日志并停止
    setTimeout(() => {
        clearInterval(interval);
        if (typeof global.exportSessionIdLogs === 'function') {
            global.exportSessionIdLogs();
        }
        console.log('监控已停止');
    }, 10000);
    
} else {
    console.log('❌ Hook系统未激活，请确保扩展已重新加载');
}
