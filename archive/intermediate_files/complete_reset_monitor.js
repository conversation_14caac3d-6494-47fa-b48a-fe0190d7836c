
// 完整机器码重置监控脚本
// 在VSCode开发者控制台中运行

console.log('🚀 开始监控完整机器码重置操作...');

// 检查Hook系统是否激活
if (typeof global.getMachineCodeResetLog === 'function') {
    console.log('✅ 完整重置Hook系统已激活');
    
    // 立即检查当前日志
    const currentLogs = global.getMachineCodeResetLog();
    console.log(`📊 当前已记录 ${currentLogs.length} 个操作`);
    
    // 显示最近的操作
    if (currentLogs.length > 0) {
        console.log('🔍 最近的操作:');
        currentLogs.slice(-5).forEach((log, index) => {
            console.log(`  ${index + 1}. [${log.relativeTime}ms] ${log.type}: ${JSON.stringify(log.details)}`);
        });
    }
    
    // 定期监控
    const monitorInterval = setInterval(() => {
        const logs = global.getMachineCodeResetLog();
        if (logs.length > currentLogs.length) {
            const newLogs = logs.slice(currentLogs.length);
            console.log(`📈 新增 ${newLogs.length} 个操作:`);
            newLogs.forEach((log, index) => {
                console.log(`  ${index + 1}. [${log.relativeTime}ms] ${log.type}: ${JSON.stringify(log.details)}`);
            });
        }
    }, 2000);
    
    // 提供手动重置功能
    console.log('\n🛠️  可用操作:');
    console.log('1. global.getMachineCodeResetLog() - 获取所有日志');
    console.log('2. global.exportMachineCodeResetLog() - 导出日志到文件');
    console.log('3. global.executeCompleteMachineCodeReset() - 执行完整重置');
    
    // 15秒后停止监控并导出日志
    setTimeout(() => {
        clearInterval(monitorInterval);
        console.log('\n📄 监控结束，导出日志...');
        if (typeof global.exportMachineCodeResetLog === 'function') {
            global.exportMachineCodeResetLog();
        }
    }, 15000);
    
} else {
    console.log('❌ 完整重置Hook系统未激活');
    console.log('请确保:');
    console.log('1. 扩展已重新加载');
    console.log('2. Hook代码已正确注入');
    console.log('3. 等待系统初始化完成');
}

// 提供手动执行重置的函数
window.manualReset = async () => {
    console.log('🔄 手动执行完整机器码重置...');
    if (typeof global.executeCompleteMachineCodeReset === 'function') {
        const result = await global.executeCompleteMachineCodeReset();
        console.log('✅ 重置完成，重置的标识符:', result);
        return result;
    } else {
        console.log('❌ 重置函数不可用');
        return null;
    }
};

console.log('\n💡 提示: 运行 manualReset() 可手动执行完整重置');
